{"ast": null, "code": "'use strict';\n\nvar NATIVE_BIND = require('../internals/function-bind-native');\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-function-prototype-bind, es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}