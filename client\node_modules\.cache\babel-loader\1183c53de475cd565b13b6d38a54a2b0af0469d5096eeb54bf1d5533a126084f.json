{"ast": null, "code": "// src/infiniteQueryObserver.ts\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { hasNextPage, hasPreviousPage, infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar InfiniteQueryObserver = class extends QueryObserver {\n  constructor(client, options) {\n    super(client, options);\n  }\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n  setOptions(options) {\n    super.setOptions({\n      ...options,\n      behavior: infiniteQueryBehavior()\n    });\n  }\n  getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return super.getOptimisticResult(options);\n  }\n  fetchNextPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: {\n          direction: \"forward\"\n        }\n      }\n    });\n  }\n  fetchPreviousPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: {\n          direction: \"backward\"\n        }\n      }\n    });\n  }\n  createResult(query, options) {\n    const {\n      state\n    } = query;\n    const parentResult = super.createResult(query, options);\n    const {\n      isFetching,\n      isRefetching,\n      isError,\n      isRefetchError\n    } = parentResult;\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction;\n    const isFetchNextPageError = isError && fetchDirection === \"forward\";\n    const isFetchingNextPage = isFetching && fetchDirection === \"forward\";\n    const isFetchPreviousPageError = isError && fetchDirection === \"backward\";\n    const isFetchingPreviousPage = isFetching && fetchDirection === \"backward\";\n    const result = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data),\n      hasPreviousPage: hasPreviousPage(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError: isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n    return result;\n  }\n};\nexport { InfiniteQueryObserver };\n//# sourceMappingURL=infiniteQueryObserver.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}