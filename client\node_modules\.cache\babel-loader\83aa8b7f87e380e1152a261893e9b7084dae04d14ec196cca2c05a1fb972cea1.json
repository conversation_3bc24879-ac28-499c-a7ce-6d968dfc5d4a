{"ast": null, "code": "import { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nconst splitProps = props => {\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = props?.theme?.unstable_sxConfig ?? defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n    sx: inSx,\n    ...other\n  } = props;\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return {\n        ...systemProps,\n        ...result\n      };\n    };\n  } else {\n    finalSx = {\n      ...systemProps,\n      ...inSx\n    };\n  }\n  return {\n    ...otherProps,\n    sx: finalSx\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}