export interface Device {
  id: string;
  deviceId: string;
  phoneNumber: string;
  deviceName: string;
  status: string;
  lastConnected?: string;
  lastDisconnected?: string;
  isActive: boolean;
  reconnectionAttempts: number;
  createdAt: string;
  updatedAt: string;
  assignedChatbots: number;
  totalMessages: number;
  hasQRCode: boolean;
}

export interface CreateDeviceRequest {
  deviceName: string;
  phoneNumber: string;
  description?: string;
}

export interface UpdateDeviceRequest {
  deviceName: string;
  description?: string;
  isActive: boolean;
}

export interface DeviceStatusInfo {
  deviceId: string;
  status: string;
  lastStatusUpdate?: string;
  isConnected: boolean;
  errorMessage?: string;
  reconnectionAttempts: number;
  nextReconnectionAttempt?: string;
  connectionInfo?: Record<string, any>;
}

export interface DeviceConnectionResult {
  success: boolean;
  qrCode?: string;
  errorMessage?: string;
  status: string;
  expiresAt?: string;
}

export interface DeviceList {
  devices: Device[];
  totalCount: number;
  connectedCount: number;
  disconnectedCount: number;
  errorCount: number;
}

export interface DeviceAnalytics {
  deviceId: string;
  deviceName: string;
  totalMessages: number;
  messagesToday: number;
  messagesThisWeek: number;
  messagesThisMonth: number;
  uptimePercentage: number;
  averageResponseTime: number;
  errorCount: number;
  lastActive: string;
  recentMetrics: DeviceMetric[];
}

export interface DeviceMetric {
  metricType: string;
  value: number;
  date: string;
  additionalData?: Record<string, any>;
}

export interface DeviceLoad {
  deviceId: string;
  deviceName: string;
  status: string;
  currentLoad: number;
  maxCapacity: number;
  loadPercentage: number;
  assignedChatbots: number;
  activeConversations: number;
}

export enum DeviceStatus {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  AUTHENTICATING = 'AUTHENTICATING',
  CONNECTED = 'CONNECTED',
  ERROR = 'ERROR',
  RECONNECTING = 'RECONNECTING',
  MAINTENANCE = 'MAINTENANCE'
}
