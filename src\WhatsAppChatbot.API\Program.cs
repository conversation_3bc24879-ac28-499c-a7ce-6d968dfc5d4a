using Microsoft.EntityFrameworkCore;
using WhatsAppChatbot.Infrastructure.Data;
using WhatsAppChatbot.Infrastructure.Data.Seeding;
using System.Text.Json;

var builder = WebApplication.CreateBuilder(args);

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Configure database connection with environment variable support
var connectionString = GetConnectionString(builder.Configuration, builder.Environment);

// Validate connection string
if (string.IsNullOrEmpty(connectionString))
{
    throw new InvalidOperationException("Database connection string is not configured. Please check your appsettings.json or environment variables.");
}

// Add Entity Framework with proper configuration
builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    if (builder.Environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }

    // Use MySQL as configured
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions =>
    {
        mySqlOptions.EnableRetryOnFailure(
            maxRetryCount: 3,
            maxRetryDelay: TimeSpan.FromSeconds(30),
            errorCodesToAdd: null);
        mySqlOptions.CommandTimeout(60);
    });
});

// Add database seeding service
builder.Services.AddScoped<DatabaseSeeder>();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5173")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

var app = builder.Build();

// Initialize database
await InitializeDatabaseAsync(app);

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowReactApp");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();

/// <summary>
/// Gets the database connection string from configuration or environment variables
/// </summary>
static string GetConnectionString(IConfiguration configuration, IWebHostEnvironment environment)
{
    // Try to get from environment variable first (for production/docker)
    var envConnectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING");
    if (!string.IsNullOrEmpty(envConnectionString))
    {
        return envConnectionString;
    }

    // Get from appsettings based on environment
    var connectionString = configuration.GetConnectionString("DefaultConnection");

    if (string.IsNullOrEmpty(connectionString))
    {
        // Fallback to default SQL Server connection string
        var server = Environment.GetEnvironmentVariable("DB_SERVER") ?? "localhost";
        var database = Environment.GetEnvironmentVariable("DB_NAME") ?? "WhatsAppChatbotDB";
        var username = Environment.GetEnvironmentVariable("DB_USERNAME") ?? "sa";
        var password = Environment.GetEnvironmentVariable("DB_PASSWORD") ?? "YourPassword123!";

        connectionString = $"Server={server};Database={database};User Id={username};Password={password};TrustServerCertificate=true;";
    }

    return connectionString;
}

/// <summary>
/// Initializes the database by creating it if it doesn't exist, applying migrations, and seeding data
/// </summary>
static async Task InitializeDatabaseAsync(WebApplication app)
{
    using var scope = app.Services.CreateScope();
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    try
    {
        logger.LogInformation("Starting database initialization...");

        var context = services.GetRequiredService<ApplicationDbContext>();
        var seeder = services.GetRequiredService<DatabaseSeeder>();

        // Ensure database is created
        logger.LogInformation("Ensuring database exists...");
        await context.Database.EnsureCreatedAsync();

        // Apply any pending migrations
        logger.LogInformation("Applying pending migrations...");
        var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
        if (pendingMigrations.Any())
        {
            logger.LogInformation($"Applying {pendingMigrations.Count()} pending migrations...");
            await context.Database.MigrateAsync();
            logger.LogInformation("Migrations applied successfully.");
        }
        else
        {
            logger.LogInformation("No pending migrations found.");
        }

        // Seed the database with initial data
        logger.LogInformation("Seeding database with initial data...");
        await seeder.SeedAsync();
        logger.LogInformation("Database seeding completed successfully.");

        logger.LogInformation("Database initialization completed successfully.");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "An error occurred while initializing the database.");
        throw;
    }
}
