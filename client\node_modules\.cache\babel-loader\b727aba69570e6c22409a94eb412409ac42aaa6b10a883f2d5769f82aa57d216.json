{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar isObject = require('../internals/is-object');\nvar document = globalThis.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}