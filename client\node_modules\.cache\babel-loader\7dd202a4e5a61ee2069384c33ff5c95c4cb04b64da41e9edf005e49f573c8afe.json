{"ast": null, "code": "// src/streamedQuery.ts\nimport { addToEnd } from \"./utils.js\";\nfunction streamedQuery({\n  queryFn,\n  refetchMode = \"reset\",\n  maxChunks\n}) {\n  return async context => {\n    const query = context.client.getQueryCache().find({\n      queryKey: context.queryKey,\n      exact: true\n    });\n    const isRefetch = !!query && query.state.data !== void 0;\n    if (isRefetch && refetchMode === \"reset\") {\n      query.setState({\n        status: \"pending\",\n        data: void 0,\n        error: null,\n        fetchStatus: \"fetching\"\n      });\n    }\n    let result = [];\n    const stream = await queryFn(context);\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break;\n      }\n      if (!isRefetch || refetchMode !== \"replace\") {\n        context.client.setQueryData(context.queryKey, (prev = []) => {\n          return addToEnd(prev, chunk, maxChunks);\n        });\n      }\n      result = addToEnd(result, chunk, maxChunks);\n    }\n    if (isRefetch && refetchMode === \"replace\" && !context.signal.aborted) {\n      context.client.setQueryData(context.queryKey, result);\n    }\n    return context.client.getQueryData(context.queryKey);\n  };\n}\nexport { streamedQuery };\n//# sourceMappingURL=streamedQuery.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}