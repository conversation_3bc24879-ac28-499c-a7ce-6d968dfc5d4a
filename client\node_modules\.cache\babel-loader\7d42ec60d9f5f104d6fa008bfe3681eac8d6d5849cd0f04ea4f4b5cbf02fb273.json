{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useControlled from '@mui/utils/useControlled';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport clamp from '@mui/utils/clamp';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport areArraysEqual from \"../utils/areArraysEqual.js\";\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction getNewValue(currentValue, step, direction, min, max) {\n  return direction === 1 ? Math.min(currentValue + step, max) : Math.max(currentValue - step, min);\n}\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  const {\n    index: closestIndex\n  } = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null) ?? {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  const doc = ownerDocument(sliderRef.current);\n  if (!sliderRef.current?.contains(doc.activeElement) || Number(doc?.activeElement?.getAttribute('data-index')) !== activeIndex) {\n    sliderRef.current?.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  // lastChangedValue is updated whenever onChange is triggered.\n  const lastChangedValue = React.useRef(null);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue ?? min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    lastChangedValue.current = value;\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleRef = useForkRef(ref, sliderRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    if (isFocusVisible(event.target)) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers?.onFocus?.(event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    if (!isFocusVisible(event.target)) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers?.onBlur?.(event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue >= maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue <= marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, lastChangedValue.current ?? newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'PageUp', 'PageDown', 'Home', 'End'].includes(event.key)) {\n      event.preventDefault();\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      // Keys actions that change the value by more than the most granular `step`\n      // value are only applied if the step not `null`.\n      // When step is `null`, the `marks` prop is used instead to define valid values.\n      if (step != null) {\n        const stepSize = event.shiftKey ? shiftStep : step;\n        switch (event.key) {\n          case 'ArrowUp':\n            newValue = getNewValue(value, stepSize, 1, min, max);\n            break;\n          case 'ArrowRight':\n            newValue = getNewValue(value, stepSize, isRtl ? -1 : 1, min, max);\n            break;\n          case 'ArrowDown':\n            newValue = getNewValue(value, stepSize, -1, min, max);\n            break;\n          case 'ArrowLeft':\n            newValue = getNewValue(value, stepSize, isRtl ? 1 : -1, min, max);\n            break;\n          case 'PageUp':\n            newValue = getNewValue(value, shiftStep, 1, min, max);\n            break;\n          case 'PageDown':\n            newValue = getNewValue(value, shiftStep, -1, min, max);\n            break;\n          case 'Home':\n            newValue = min;\n            break;\n          case 'End':\n            newValue = max;\n            break;\n          default:\n            break;\n        }\n      } else if (marks) {\n        const maxMarksValue = marksValues[marksValues.length - 1];\n        const currentMarkIndex = marksValues.indexOf(value);\n        const decrementKeys = [isRtl ? 'ArrowRight' : 'ArrowLeft', 'ArrowDown', 'PageDown', 'Home'];\n        const incrementKeys = [isRtl ? 'ArrowLeft' : 'ArrowRight', 'ArrowUp', 'PageUp', 'End'];\n        if (decrementKeys.includes(event.key)) {\n          if (currentMarkIndex === 0) {\n            newValue = marksValues[0];\n          } else {\n            newValue = marksValues[currentMarkIndex - 1];\n          }\n        } else if (incrementKeys.includes(event.key)) {\n          if (currentMarkIndex === marksValues.length - 1) {\n            newValue = maxMarksValue;\n          } else {\n            newValue = marksValues[currentMarkIndex + 1];\n          }\n        }\n      }\n      if (newValue != null) {\n        changeValue(event, newValue);\n      }\n    }\n    otherHandlers?.onKeyDown?.(event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      document.activeElement?.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    otherHandlers.onChange?.(event);\n    // this handles value change by Pointer or Touch events\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.startsWith('vertical')) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.includes('-reverse')) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, lastChangedValue.current ?? newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    otherHandlers.onMouseDown?.(event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      ...externalProps,\n      ref: handleRef,\n      ...mergedEventHandlers\n    };\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    otherHandlers.onMouseOver?.(event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    otherHandlers.onMouseLeave?.(event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return {\n      ...externalProps,\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  let cssWritingMode;\n  if (orientation === 'vertical') {\n    cssWritingMode = isRtl ? 'vertical-rl' : 'vertical-lr';\n  }\n  const getHiddenInputProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : parameters.step ?? undefined,\n      disabled,\n      ...externalProps,\n      ...mergedEventHandlers,\n      style: {\n        ...visuallyHidden,\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%',\n        writingMode: cssWritingMode\n      }\n    };\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}