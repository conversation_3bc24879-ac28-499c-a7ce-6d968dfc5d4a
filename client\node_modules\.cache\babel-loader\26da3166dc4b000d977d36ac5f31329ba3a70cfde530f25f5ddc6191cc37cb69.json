{"ast": null, "code": "import borders from \"../borders/index.js\";\nimport display from \"../display/index.js\";\nimport flexbox from \"../flexbox/index.js\";\nimport grid from \"../cssGrid/index.js\";\nimport positions from \"../positions/index.js\";\nimport palette from \"../palette/index.js\";\nimport shadows from \"../shadows/index.js\";\nimport sizing from \"../sizing/index.js\";\nimport spacing from \"../spacing/index.js\";\nimport typography from \"../typography/index.js\";\nconst filterPropsMapping = {\n  borders: borders.filterProps,\n  display: display.filterProps,\n  flexbox: flexbox.filterProps,\n  grid: grid.filterProps,\n  positions: positions.filterProps,\n  palette: palette.filterProps,\n  shadows: shadows.filterProps,\n  sizing: sizing.filterProps,\n  spacing: spacing.filterProps,\n  typography: typography.filterProps\n};\nexport const styleFunctionMapping = {\n  borders,\n  display,\n  flexbox,\n  grid,\n  positions,\n  palette,\n  shadows,\n  sizing,\n  spacing,\n  typography\n};\nexport const propToStyleFunction = Object.keys(filterPropsMapping).reduce((acc, styleFnName) => {\n  filterPropsMapping[styleFnName].forEach(propName => {\n    acc[propName] = styleFunctionMapping[styleFnName];\n  });\n  return acc;\n}, {});\nfunction getThemeValue(prop, value, theme) {\n  const inputProps = {\n    [prop]: value,\n    theme\n  };\n  const styleFunction = propToStyleFunction[prop];\n  return styleFunction ? styleFunction(inputProps) : {\n    [prop]: value\n  };\n}\nexport default getThemeValue;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}