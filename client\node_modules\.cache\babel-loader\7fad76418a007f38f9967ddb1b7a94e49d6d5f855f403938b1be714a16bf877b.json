{"ast": null, "code": "// src/queriesObserver.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { replaceEqualDeep } from \"./utils.js\";\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nvar QueriesObserver = class extends Subscribable {\n  #client;\n  #result;\n  #queries;\n  #options;\n  #observers;\n  #combinedResult;\n  #lastCombine;\n  #lastResult;\n  #observerMatches = [];\n  constructor(client, queries, options) {\n    super();\n    this.#client = client;\n    this.#options = options;\n    this.#queries = [];\n    this.#observers = [];\n    this.#result = [];\n    this.setQueries(queries);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n    }\n  }\n  onUnsubscribe() {\n    if (!this.listeners.size) {\n      this.destroy();\n    }\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.#observers.forEach(observer => {\n      observer.destroy();\n    });\n  }\n  setQueries(queries, options) {\n    this.#queries = queries;\n    this.#options = options;\n    if (process.env.NODE_ENV !== \"production\") {\n      const queryHashes = queries.map(query => this.#client.defaultQueryOptions(query).queryHash);\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\"[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.\");\n      }\n    }\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers;\n      const newObserverMatches = this.#findMatchingObservers(this.#queries);\n      this.#observerMatches = newObserverMatches;\n      newObserverMatches.forEach(match => match.observer.setOptions(match.defaultedQueryOptions));\n      const newObservers = newObserverMatches.map(match => match.observer);\n      const newResult = newObservers.map(observer => observer.getCurrentResult());\n      const hasIndexChange = newObservers.some((observer, index) => observer !== prevObservers[index]);\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n      this.#observers = newObservers;\n      this.#result = newResult;\n      if (!this.hasListeners()) {\n        return;\n      }\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n      this.#notify();\n    });\n  }\n  getCurrentResult() {\n    return this.#result;\n  }\n  getQueries() {\n    return this.#observers.map(observer => observer.getCurrentQuery());\n  }\n  getObservers() {\n    return this.#observers;\n  }\n  getOptimisticResult(queries, combine) {\n    const matches = this.#findMatchingObservers(queries);\n    const result = matches.map(match => match.observer.getOptimisticResult(match.defaultedQueryOptions));\n    return [result, r => {\n      return this.#combineResult(r ?? result, combine);\n    }, () => {\n      return this.#trackResult(result, matches);\n    }];\n  }\n  #trackResult(result, matches) {\n    return matches.map((match, index) => {\n      const observerResult = result[index];\n      return !match.defaultedQueryOptions.notifyOnChangeProps ? match.observer.trackResult(observerResult, accessedProp => {\n        matches.forEach(m => {\n          m.observer.trackProp(accessedProp);\n        });\n      }) : observerResult;\n    });\n  }\n  #combineResult(input, combine) {\n    if (combine) {\n      if (!this.#combinedResult || this.#result !== this.#lastResult || combine !== this.#lastCombine) {\n        this.#lastCombine = combine;\n        this.#lastResult = this.#result;\n        this.#combinedResult = replaceEqualDeep(this.#combinedResult, combine(input));\n      }\n      return this.#combinedResult;\n    }\n    return input;\n  }\n  #findMatchingObservers(queries) {\n    const prevObserversMap = new Map(this.#observers.map(observer => [observer.options.queryHash, observer]));\n    const observers = [];\n    queries.forEach(options => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options);\n      const match = prevObserversMap.get(defaultedOptions.queryHash);\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        });\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new QueryObserver(this.#client, defaultedOptions)\n        });\n      }\n    });\n    return observers;\n  }\n  #onUpdate(observer, result) {\n    const index = this.#observers.indexOf(observer);\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result);\n      this.#notify();\n    }\n  }\n  #notify() {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult;\n      const newTracked = this.#trackResult(this.#result, this.#observerMatches);\n      const newResult = this.#combineResult(newTracked, this.#options?.combine);\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach(listener => {\n            listener(this.#result);\n          });\n        });\n      }\n    }\n  }\n};\nexport { QueriesObserver };\n//# sourceMappingURL=queriesObserver.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}