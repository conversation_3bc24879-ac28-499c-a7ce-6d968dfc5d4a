import { apiService } from './api';
import {
  Device,
  CreateDeviceRequest,
  UpdateDeviceRequest,
  DeviceStatusInfo,
  DeviceConnectionResult,
  DeviceList,
  DeviceAnalytics,
  DeviceLoad
} from '../types/device';

class DeviceService {
  async getDevices(): Promise<DeviceList> {
    return apiService.get<DeviceList>('/devices');
  }

  async getDevice(id: string): Promise<Device> {
    return apiService.get<Device>(`/devices/${id}`);
  }

  async createDevice(deviceData: CreateDeviceRequest): Promise<Device> {
    return apiService.post<Device>('/devices', deviceData);
  }

  async updateDevice(id: string, deviceData: UpdateDeviceRequest): Promise<Device> {
    return apiService.put<Device>(`/devices/${id}`, deviceData);
  }

  async deleteDevice(id: string): Promise<void> {
    return apiService.delete(`/devices/${id}`);
  }

  async connectDevice(id: string): Promise<DeviceConnectionResult> {
    return apiService.post<DeviceConnectionResult>(`/devices/${id}/connect`);
  }

  async disconnectDevice(id: string): Promise<void> {
    return apiService.post(`/devices/${id}/disconnect`);
  }

  async getQRCode(id: string): Promise<{ qrCode: string }> {
    return apiService.get<{ qrCode: string }>(`/devices/${id}/qr`);
  }

  async getDeviceStatus(id: string): Promise<DeviceStatusInfo> {
    return apiService.get<DeviceStatusInfo>(`/devices/${id}/status`);
  }

  async getAvailableDevices(): Promise<Device[]> {
    return apiService.get<Device[]>('/devices/available');
  }

  async getDeviceAnalytics(id: string): Promise<DeviceAnalytics> {
    return apiService.get<DeviceAnalytics>(`/devices/${id}/analytics`);
  }

  async getDeviceLoad(): Promise<DeviceLoad[]> {
    return apiService.get<DeviceLoad[]>('/devices/load');
  }

  // Real-time status polling
  async startStatusPolling(deviceId: string, callback: (status: DeviceStatusInfo) => void, interval: number = 5000): Promise<() => void> {
    const pollStatus = async () => {
      try {
        const status = await this.getDeviceStatus(deviceId);
        callback(status);
      } catch (error) {
        console.error('Error polling device status:', error);
      }
    };

    // Initial poll
    await pollStatus();

    // Set up interval
    const intervalId = setInterval(pollStatus, interval);

    // Return cleanup function
    return () => clearInterval(intervalId);
  }

  // Batch operations
  async connectMultipleDevices(deviceIds: string[]): Promise<DeviceConnectionResult[]> {
    const promises = deviceIds.map(id => this.connectDevice(id));
    return Promise.all(promises);
  }

  async disconnectMultipleDevices(deviceIds: string[]): Promise<void> {
    const promises = deviceIds.map(id => this.disconnectDevice(id));
    await Promise.all(promises);
  }

  async deleteMultipleDevices(deviceIds: string[]): Promise<void> {
    const promises = deviceIds.map(id => this.deleteDevice(id));
    await Promise.all(promises);
  }

  // Device health check
  async performHealthCheck(deviceId: string): Promise<{
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const device = await this.getDevice(deviceId);
      const status = await this.getDeviceStatus(deviceId);
      
      const issues: string[] = [];
      const recommendations: string[] = [];
      
      // Check connection status
      if (!status.isConnected) {
        issues.push('Device is not connected');
        recommendations.push('Try reconnecting the device');
      }
      
      // Check reconnection attempts
      if (status.reconnectionAttempts > 3) {
        issues.push('Multiple reconnection attempts detected');
        recommendations.push('Check device network connection');
      }
      
      // Check last activity
      if (device.lastConnected) {
        const lastConnected = new Date(device.lastConnected);
        const hoursSinceLastConnection = (Date.now() - lastConnected.getTime()) / (1000 * 60 * 60);
        
        if (hoursSinceLastConnection > 24) {
          issues.push('Device has been offline for more than 24 hours');
          recommendations.push('Verify device is powered on and has internet access');
        }
      }
      
      return {
        isHealthy: issues.length === 0,
        issues,
        recommendations
      };
    } catch (error) {
      return {
        isHealthy: false,
        issues: ['Unable to perform health check'],
        recommendations: ['Check device connectivity and try again']
      };
    }
  }
}

export const deviceService = new DeviceService();
export default deviceService;
