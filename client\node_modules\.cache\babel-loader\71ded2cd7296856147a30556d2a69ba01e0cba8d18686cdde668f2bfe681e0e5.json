{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Ripple(props) {\n  const {\n    className,\n    classes,\n    pulsate = false,\n    rippleX,\n    rippleY,\n    rippleSize,\n    in: inProp,\n    onExited,\n    timeout\n  } = props;\n  const [leaving, setLeaving] = React.useState(false);\n  const rippleClassName = clsx(className, classes.ripple, classes.rippleVisible, pulsate && classes.ripplePulsate);\n  const rippleStyles = {\n    width: rippleSize,\n    height: rippleSize,\n    top: -(rippleSize / 2) + rippleY,\n    left: -(rippleSize / 2) + rippleX\n  };\n  const childClassName = clsx(classes.child, leaving && classes.childLeaving, pulsate && classes.childPulsate);\n  if (!inProp && !leaving) {\n    setLeaving(true);\n  }\n  React.useEffect(() => {\n    if (!inProp && onExited != null) {\n      // react-transition-group#onExited\n      const timeoutId = setTimeout(onExited, timeout);\n      return () => {\n        clearTimeout(timeoutId);\n      };\n    }\n    return undefined;\n  }, [onExited, inProp, timeout]);\n  return /*#__PURE__*/_jsx(\"span\", {\n    className: rippleClassName,\n    style: rippleStyles,\n    children: /*#__PURE__*/_jsx(\"span\", {\n      className: childClassName\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? Ripple.propTypes /* remove-proptypes */ = {\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object.isRequired,\n  className: PropTypes.string,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore - injected from TransitionGroup\n   */\n  onExited: PropTypes.func,\n  /**\n   * If `true`, the ripple pulsates, typically indicating the keyboard focus state of an element.\n   */\n  pulsate: PropTypes.bool,\n  /**\n   * Diameter of the ripple.\n   */\n  rippleSize: PropTypes.number,\n  /**\n   * Horizontal position of the ripple center.\n   */\n  rippleX: PropTypes.number,\n  /**\n   * Vertical position of the ripple center.\n   */\n  rippleY: PropTypes.number,\n  /**\n   * exit delay\n   */\n  timeout: PropTypes.number.isRequired\n} : void 0;\nexport default Ripple;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}