{"ast": null, "code": "// src/hydration.ts\nimport { tryResolveSync } from \"./thenable.js\";\nfunction defaultTransformerFn(data) {\n  return data;\n}\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n    ...(mutation.options.scope && {\n      scope: mutation.options.scope\n    }),\n    ...(mutation.meta && {\n      meta: mutation.meta\n    })\n  };\n}\nfunction dehydrateQuery(query, serializeData, shouldRedactErrors) {\n  return {\n    dehydratedAt: Date.now(),\n    state: {\n      ...query.state,\n      ...(query.state.data !== void 0 && {\n        data: serializeData(query.state.data)\n      })\n    },\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n    ...(query.state.status === \"pending\" && {\n      promise: query.promise?.then(serializeData).catch(error => {\n        if (!shouldRedactErrors(error)) {\n          return Promise.reject(error);\n        }\n        if (process.env.NODE_ENV !== \"production\") {\n          console.error(`A query that was dehydrated as pending ended up rejecting. [${query.queryHash}]: ${error}; The error will be redacted in production builds`);\n        }\n        return Promise.reject(new Error(\"redacted\"));\n      })\n    }),\n    ...(query.meta && {\n      meta: query.meta\n    })\n  };\n}\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === \"success\";\n}\nfunction defaultShouldRedactErrors(_) {\n  return true;\n}\nfunction dehydrate(client, options = {}) {\n  const filterMutation = options.shouldDehydrateMutation ?? client.getDefaultOptions().dehydrate?.shouldDehydrateMutation ?? defaultShouldDehydrateMutation;\n  const mutations = client.getMutationCache().getAll().flatMap(mutation => filterMutation(mutation) ? [dehydrateMutation(mutation)] : []);\n  const filterQuery = options.shouldDehydrateQuery ?? client.getDefaultOptions().dehydrate?.shouldDehydrateQuery ?? defaultShouldDehydrateQuery;\n  const shouldRedactErrors = options.shouldRedactErrors ?? client.getDefaultOptions().dehydrate?.shouldRedactErrors ?? defaultShouldRedactErrors;\n  const serializeData = options.serializeData ?? client.getDefaultOptions().dehydrate?.serializeData ?? defaultTransformerFn;\n  const queries = client.getQueryCache().getAll().flatMap(query => filterQuery(query) ? [dehydrateQuery(query, serializeData, shouldRedactErrors)] : []);\n  return {\n    mutations,\n    queries\n  };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== \"object\" || dehydratedState === null) {\n    return;\n  }\n  const mutationCache = client.getMutationCache();\n  const queryCache = client.getQueryCache();\n  const deserializeData = options?.defaultOptions?.deserializeData ?? client.getDefaultOptions().hydrate?.deserializeData ?? defaultTransformerFn;\n  const mutations = dehydratedState.mutations || [];\n  const queries = dehydratedState.queries || [];\n  mutations.forEach(({\n    state,\n    ...mutationOptions\n  }) => {\n    mutationCache.build(client, {\n      ...client.getDefaultOptions().hydrate?.mutations,\n      ...options?.defaultOptions?.mutations,\n      ...mutationOptions\n    }, state);\n  });\n  queries.forEach(({\n    queryKey,\n    state,\n    queryHash,\n    meta,\n    promise,\n    dehydratedAt\n  }) => {\n    const syncData = promise ? tryResolveSync(promise) : void 0;\n    const rawData = state.data === void 0 ? syncData?.data : state.data;\n    const data = rawData === void 0 ? rawData : deserializeData(rawData);\n    let query = queryCache.get(queryHash);\n    const existingQueryIsPending = query?.state.status === \"pending\";\n    const existingQueryIsFetching = query?.state.fetchStatus === \"fetching\";\n    if (query) {\n      const hasNewerSyncData = syncData &&\n      // We only need this undefined check to handle older dehydration\n      // payloads that might not have dehydratedAt\n      dehydratedAt !== void 0 && dehydratedAt > query.state.dataUpdatedAt;\n      if (state.dataUpdatedAt > query.state.dataUpdatedAt || hasNewerSyncData) {\n        const {\n          fetchStatus: _ignored,\n          ...serializedState\n        } = state;\n        query.setState({\n          ...serializedState,\n          data\n        });\n      }\n    } else {\n      query = queryCache.build(client, {\n        ...client.getDefaultOptions().hydrate?.queries,\n        ...options?.defaultOptions?.queries,\n        queryKey,\n        queryHash,\n        meta\n      },\n      // Reset fetch status to idle to avoid\n      // query being stuck in fetching state upon hydration\n      {\n        ...state,\n        data,\n        fetchStatus: \"idle\",\n        status: data !== void 0 ? \"success\" : state.status\n      });\n    }\n    if (promise && !existingQueryIsPending && !existingQueryIsFetching && (\n    // Only hydrate if dehydration is newer than any existing data,\n    // this is always true for new queries\n    dehydratedAt === void 0 || dehydratedAt > query.state.dataUpdatedAt)) {\n      void query.fetch(void 0, {\n        // RSC transformed promises are not thenable\n        initialPromise: Promise.resolve(promise).then(deserializeData)\n      });\n    }\n  });\n}\nexport { defaultShouldDehydrateMutation, defaultShouldDehydrateQuery, dehydrate, hydrate };\n//# sourceMappingURL=hydration.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}