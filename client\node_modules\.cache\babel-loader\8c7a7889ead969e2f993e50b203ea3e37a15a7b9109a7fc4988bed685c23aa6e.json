{"ast": null, "code": "'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}