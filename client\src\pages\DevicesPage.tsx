import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Tooltip,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  PhoneAndroid,
  SignalWifi4Bar,
  SignalWifiOff,
  Error as ErrorIcon,
  QrCode,
  Delete,
  Edit,
  PowerSettingsNew,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { deviceService } from '../services/deviceService';
import { Device, CreateDeviceRequest, DeviceStatus } from '../types/device';

const DevicesPage: React.FC = () => {
  const queryClient = useQueryClient();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [qrDialogOpen, setQrDialogOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [qrCode, setQrCode] = useState<string>('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuDevice, setMenuDevice] = useState<Device | null>(null);

  const [newDevice, setNewDevice] = useState<CreateDeviceRequest>({
    deviceName: '',
    phoneNumber: '',
    description: '',
  });

  // Fetch devices
  const { data: deviceList, isLoading, error } = useQuery({
    queryKey: ['devices'],
    queryFn: () => deviceService.getDevices(),
    refetchInterval: 10000, // Refetch every 10 seconds for real-time updates
  });

  // Create device mutation
  const createDeviceMutation = useMutation({
    mutationFn: (deviceData: CreateDeviceRequest) => deviceService.createDevice(deviceData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['devices'] });
      setCreateDialogOpen(false);
      setNewDevice({ deviceName: '', phoneNumber: '', description: '' });
    },
  });

  // Connect device mutation
  const connectDeviceMutation = useMutation({
    mutationFn: (deviceId: string) => deviceService.connectDevice(deviceId),
    onSuccess: (result, deviceId) => {
      queryClient.invalidateQueries({ queryKey: ['devices'] });
      if (result.qrCode) {
        setQrCode(result.qrCode);
        setQrDialogOpen(true);
      }
    },
  });

  // Disconnect device mutation
  const disconnectDeviceMutation = useMutation({
    mutationFn: (deviceId: string) => deviceService.disconnectDevice(deviceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['devices'] });
    },
  });

  // Delete device mutation
  const deleteDeviceMutation = useMutation({
    mutationFn: (deviceId: string) => deviceService.deleteDevice(deviceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['devices'] });
    },
  });

  const handleCreateDevice = () => {
    if (!newDevice.deviceName || !newDevice.phoneNumber) {
      return;
    }
    createDeviceMutation.mutate(newDevice);
  };

  const handleConnectDevice = (device: Device) => {
    setSelectedDevice(device);
    connectDeviceMutation.mutate(device.id);
  };

  const handleDisconnectDevice = (device: Device) => {
    disconnectDeviceMutation.mutate(device.id);
    handleMenuClose();
  };

  const handleDeleteDevice = (device: Device) => {
    if (window.confirm(`Are you sure you want to delete device "${device.deviceName}"?`)) {
      deleteDeviceMutation.mutate(device.id);
    }
    handleMenuClose();
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, device: Device) => {
    setAnchorEl(event.currentTarget);
    setMenuDevice(device);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuDevice(null);
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'CONNECTED':
        return 'success';
      case 'CONNECTING':
      case 'AUTHENTICATING':
        return 'warning';
      case 'DISCONNECTED':
        return 'default';
      case 'ERROR':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toUpperCase()) {
      case 'CONNECTED':
        return <SignalWifi4Bar />;
      case 'CONNECTING':
      case 'AUTHENTICATING':
        return <CircularProgress size={16} />;
      case 'DISCONNECTED':
        return <SignalWifiOff />;
      case 'ERROR':
        return <ErrorIcon />;
      default:
        return <SignalWifiOff />;
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load devices. Please try again.
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Device Manager
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Add Device
        </Button>
      </Box>

      {/* Device Statistics */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Devices
              </Typography>
              <Typography variant="h4">
                {deviceList?.totalCount || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Connected
              </Typography>
              <Typography variant="h4" color="success.main">
                {deviceList?.connectedCount || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Disconnected
              </Typography>
              <Typography variant="h4" color="warning.main">
                {deviceList?.disconnectedCount || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Errors
              </Typography>
              <Typography variant="h4" color="error.main">
                {deviceList?.errorCount || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Device List */}
      <Grid container spacing={2}>
        {deviceList?.devices.map((device) => (
          <Grid item xs={12} sm={6} md={4} key={device.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                  <Box display="flex" alignItems="center" mb={1}>
                    <PhoneAndroid sx={{ mr: 1 }} />
                    <Typography variant="h6" component="h2">
                      {device.deviceName}
                    </Typography>
                  </Box>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, device)}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </Box>

                <Typography color="textSecondary" gutterBottom>
                  {device.phoneNumber}
                </Typography>

                <Box display="flex" alignItems="center" mb={2}>
                  <Chip
                    icon={getStatusIcon(device.status)}
                    label={device.status}
                    color={getStatusColor(device.status) as any}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" color="textSecondary" mb={2}>
                  Assigned Chatbots: {device.assignedChatbots}
                </Typography>

                {device.status === 'DISCONNECTED' && (
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<PowerSettingsNew />}
                    onClick={() => handleConnectDevice(device)}
                    disabled={connectDeviceMutation.isPending}
                    fullWidth
                  >
                    Connect
                  </Button>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Device Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleMenuClose()}>
          <Edit sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        {menuDevice?.status === 'CONNECTED' && (
          <MenuItem onClick={() => handleDisconnectDevice(menuDevice)}>
            <PowerSettingsNew sx={{ mr: 1 }} />
            Disconnect
          </MenuItem>
        )}
        <MenuItem onClick={() => handleDeleteDevice(menuDevice!)}>
          <Delete sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create Device Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Device</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Device Name"
            fullWidth
            variant="outlined"
            value={newDevice.deviceName}
            onChange={(e) => setNewDevice({ ...newDevice, deviceName: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Phone Number"
            fullWidth
            variant="outlined"
            value={newDevice.phoneNumber}
            onChange={(e) => setNewDevice({ ...newDevice, phoneNumber: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description (Optional)"
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            value={newDevice.description}
            onChange={(e) => setNewDevice({ ...newDevice, description: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateDevice}
            variant="contained"
            disabled={createDeviceMutation.isPending || !newDevice.deviceName || !newDevice.phoneNumber}
          >
            {createDeviceMutation.isPending ? <CircularProgress size={20} /> : 'Add Device'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* QR Code Dialog */}
      <Dialog open={qrDialogOpen} onClose={() => setQrDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Scan QR Code</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" alignItems="center" p={2}>
            <Typography variant="body1" mb={2} textAlign="center">
              Scan this QR code with WhatsApp on your phone to connect the device.
            </Typography>
            {qrCode && (
              <Box
                component="img"
                src={`data:image/png;base64,${qrCode}`}
                alt="QR Code"
                sx={{ maxWidth: '100%', height: 'auto' }}
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setQrDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DevicesPage;
