{"ast": null, "code": "// src/query.ts\nimport { ensureQueryFn, noop, replaceData, resolveEnabled, resolveStaleTime, skipToken, timeUntilStale } from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = {\n      ...this.#defaultOptions,\n      ...options\n    };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({\n      type: \"setState\",\n      state,\n      setStateOptions\n    });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(observer => resolveEnabled(observer.options.enabled, this) !== false);\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStatic() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(observer => resolveStaleTime(observer.options.staleTime, this) === \"static\");\n    }\n    return false;\n  }\n  isStale() {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(observer => observer.getCurrentResult().isStale);\n    }\n    return this.state.data === void 0 || this.state.isInvalidated;\n  }\n  isStaleByTime(staleTime = 0) {\n    if (this.state.data === void 0) {\n      return true;\n    }\n    if (staleTime === \"static\") {\n      return false;\n    }\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({\n        type: \"observerAdded\",\n        query: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({\n        type: \"observerRemoved\",\n        query: this,\n        observer\n      });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({\n        type: \"invalidate\"\n      });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({\n          silent: true\n        });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = object => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const createQueryFnContext = () => {\n        const queryFnContext2 = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta\n        };\n        addSignalProperty(queryFnContext2);\n        return queryFnContext2;\n      };\n      const queryFnContext = createQueryFnContext();\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(queryFn, queryFnContext, this);\n      }\n      return queryFn(queryFnContext);\n    };\n    const createFetchContext = () => {\n      const context2 = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn\n      };\n      addSignalProperty(context2);\n      return context2;\n    };\n    const context = createFetchContext();\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({\n        type: \"fetch\",\n        meta: context.fetchOptions?.meta\n      });\n    }\n    const onError = error => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(error, this);\n        this.#cache.config.onSettled?.(this.state.data, error, this);\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: data => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(data, this.state.error, this);\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue: () => {\n        this.#dispatch({\n          type: \"continue\"\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...(!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return {\n              ...this.#revertState,\n              fetchStatus: \"idle\"\n            };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({\n        query: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...(data === void 0 && {\n      error: null,\n      status: \"pending\"\n    })\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport { Query, fetchState };\n//# sourceMappingURL=query.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}