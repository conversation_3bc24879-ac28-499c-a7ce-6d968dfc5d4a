{"ast": null, "code": "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {});\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = value => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = reason => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then(result => {\n    data = result;\n    return result;\n  })?.catch(noop);\n  if (data !== void 0) {\n    return {\n      data\n    };\n  }\n  return void 0;\n}\nexport { pendingThenable, tryResolveSync };\n//# sourceMappingURL=thenable.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}