{"ast": null, "code": "import { apiService } from './api';\nclass DeviceService {\n  async getDevices() {\n    return apiService.get('/devices');\n  }\n  async getDevice(id) {\n    return apiService.get(`/devices/${id}`);\n  }\n  async createDevice(deviceData) {\n    return apiService.post('/devices', deviceData);\n  }\n  async updateDevice(id, deviceData) {\n    return apiService.put(`/devices/${id}`, deviceData);\n  }\n  async deleteDevice(id) {\n    return apiService.delete(`/devices/${id}`);\n  }\n  async connectDevice(id) {\n    return apiService.post(`/devices/${id}/connect`);\n  }\n  async disconnectDevice(id) {\n    return apiService.post(`/devices/${id}/disconnect`);\n  }\n  async getQRCode(id) {\n    return apiService.get(`/devices/${id}/qr`);\n  }\n  async getDeviceStatus(id) {\n    return apiService.get(`/devices/${id}/status`);\n  }\n  async getAvailableDevices() {\n    return apiService.get('/devices/available');\n  }\n  async getDeviceAnalytics(id) {\n    return apiService.get(`/devices/${id}/analytics`);\n  }\n  async getDeviceLoad() {\n    return apiService.get('/devices/load');\n  }\n\n  // Real-time status polling\n  async startStatusPolling(deviceId, callback, interval = 5000) {\n    const pollStatus = async () => {\n      try {\n        const status = await this.getDeviceStatus(deviceId);\n        callback(status);\n      } catch (error) {\n        console.error('Error polling device status:', error);\n      }\n    };\n\n    // Initial poll\n    await pollStatus();\n\n    // Set up interval\n    const intervalId = setInterval(pollStatus, interval);\n\n    // Return cleanup function\n    return () => clearInterval(intervalId);\n  }\n\n  // Batch operations\n  async connectMultipleDevices(deviceIds) {\n    const promises = deviceIds.map(id => this.connectDevice(id));\n    return Promise.all(promises);\n  }\n  async disconnectMultipleDevices(deviceIds) {\n    const promises = deviceIds.map(id => this.disconnectDevice(id));\n    await Promise.all(promises);\n  }\n  async deleteMultipleDevices(deviceIds) {\n    const promises = deviceIds.map(id => this.deleteDevice(id));\n    await Promise.all(promises);\n  }\n\n  // Device health check\n  async performHealthCheck(deviceId) {\n    try {\n      const device = await this.getDevice(deviceId);\n      const status = await this.getDeviceStatus(deviceId);\n      const issues = [];\n      const recommendations = [];\n\n      // Check connection status\n      if (!status.isConnected) {\n        issues.push('Device is not connected');\n        recommendations.push('Try reconnecting the device');\n      }\n\n      // Check reconnection attempts\n      if (status.reconnectionAttempts > 3) {\n        issues.push('Multiple reconnection attempts detected');\n        recommendations.push('Check device network connection');\n      }\n\n      // Check last activity\n      if (device.lastConnected) {\n        const lastConnected = new Date(device.lastConnected);\n        const hoursSinceLastConnection = (Date.now() - lastConnected.getTime()) / (1000 * 60 * 60);\n        if (hoursSinceLastConnection > 24) {\n          issues.push('Device has been offline for more than 24 hours');\n          recommendations.push('Verify device is powered on and has internet access');\n        }\n      }\n      return {\n        isHealthy: issues.length === 0,\n        issues,\n        recommendations\n      };\n    } catch (error) {\n      return {\n        isHealthy: false,\n        issues: ['Unable to perform health check'],\n        recommendations: ['Check device connectivity and try again']\n      };\n    }\n  }\n}\nexport const deviceService = new DeviceService();\nexport default deviceService;", "map": {"version": 3, "names": ["apiService", "DeviceService", "getDevices", "get", "getDevice", "id", "createDevice", "deviceData", "post", "updateDevice", "put", "deleteDevice", "delete", "connectDevice", "disconnectDevice", "getQRCode", "getDeviceStatus", "getAvailableDevices", "getDeviceAnalytics", "getDeviceLoad", "startStatusPolling", "deviceId", "callback", "interval", "pollStatus", "status", "error", "console", "intervalId", "setInterval", "clearInterval", "connectMultipleDevices", "deviceIds", "promises", "map", "Promise", "all", "disconnectMultipleDevices", "deleteMultipleDevices", "performHealthCheck", "device", "issues", "recommendations", "isConnected", "push", "reconnectionAttempts", "lastConnected", "Date", "hoursSinceLastConnection", "now", "getTime", "is<PERSON><PERSON><PERSON>", "length", "deviceService"], "sources": ["D:/React Chatbot/client/src/services/deviceService.ts"], "sourcesContent": ["import { apiService } from './api';\nimport {\n  Device,\n  CreateDeviceRequest,\n  UpdateDeviceRequest,\n  DeviceStatusInfo,\n  DeviceConnectionResult,\n  DeviceList,\n  DeviceAnalytics,\n  DeviceLoad\n} from '../types/device';\n\nclass DeviceService {\n  async getDevices(): Promise<DeviceList> {\n    return apiService.get<DeviceList>('/devices');\n  }\n\n  async getDevice(id: string): Promise<Device> {\n    return apiService.get<Device>(`/devices/${id}`);\n  }\n\n  async createDevice(deviceData: CreateDeviceRequest): Promise<Device> {\n    return apiService.post<Device>('/devices', deviceData);\n  }\n\n  async updateDevice(id: string, deviceData: UpdateDeviceRequest): Promise<Device> {\n    return apiService.put<Device>(`/devices/${id}`, deviceData);\n  }\n\n  async deleteDevice(id: string): Promise<void> {\n    return apiService.delete(`/devices/${id}`);\n  }\n\n  async connectDevice(id: string): Promise<DeviceConnectionResult> {\n    return apiService.post<DeviceConnectionResult>(`/devices/${id}/connect`);\n  }\n\n  async disconnectDevice(id: string): Promise<void> {\n    return apiService.post(`/devices/${id}/disconnect`);\n  }\n\n  async getQRCode(id: string): Promise<{ qrCode: string }> {\n    return apiService.get<{ qrCode: string }>(`/devices/${id}/qr`);\n  }\n\n  async getDeviceStatus(id: string): Promise<DeviceStatusInfo> {\n    return apiService.get<DeviceStatusInfo>(`/devices/${id}/status`);\n  }\n\n  async getAvailableDevices(): Promise<Device[]> {\n    return apiService.get<Device[]>('/devices/available');\n  }\n\n  async getDeviceAnalytics(id: string): Promise<DeviceAnalytics> {\n    return apiService.get<DeviceAnalytics>(`/devices/${id}/analytics`);\n  }\n\n  async getDeviceLoad(): Promise<DeviceLoad[]> {\n    return apiService.get<DeviceLoad[]>('/devices/load');\n  }\n\n  // Real-time status polling\n  async startStatusPolling(deviceId: string, callback: (status: DeviceStatus) => void, interval: number = 5000): Promise<() => void> {\n    const pollStatus = async () => {\n      try {\n        const status = await this.getDeviceStatus(deviceId);\n        callback(status);\n      } catch (error) {\n        console.error('Error polling device status:', error);\n      }\n    };\n\n    // Initial poll\n    await pollStatus();\n\n    // Set up interval\n    const intervalId = setInterval(pollStatus, interval);\n\n    // Return cleanup function\n    return () => clearInterval(intervalId);\n  }\n\n  // Batch operations\n  async connectMultipleDevices(deviceIds: string[]): Promise<DeviceConnectionResult[]> {\n    const promises = deviceIds.map(id => this.connectDevice(id));\n    return Promise.all(promises);\n  }\n\n  async disconnectMultipleDevices(deviceIds: string[]): Promise<void> {\n    const promises = deviceIds.map(id => this.disconnectDevice(id));\n    await Promise.all(promises);\n  }\n\n  async deleteMultipleDevices(deviceIds: string[]): Promise<void> {\n    const promises = deviceIds.map(id => this.deleteDevice(id));\n    await Promise.all(promises);\n  }\n\n  // Device health check\n  async performHealthCheck(deviceId: string): Promise<{\n    isHealthy: boolean;\n    issues: string[];\n    recommendations: string[];\n  }> {\n    try {\n      const device = await this.getDevice(deviceId);\n      const status = await this.getDeviceStatus(deviceId);\n      \n      const issues: string[] = [];\n      const recommendations: string[] = [];\n      \n      // Check connection status\n      if (!status.isConnected) {\n        issues.push('Device is not connected');\n        recommendations.push('Try reconnecting the device');\n      }\n      \n      // Check reconnection attempts\n      if (status.reconnectionAttempts > 3) {\n        issues.push('Multiple reconnection attempts detected');\n        recommendations.push('Check device network connection');\n      }\n      \n      // Check last activity\n      if (device.lastConnected) {\n        const lastConnected = new Date(device.lastConnected);\n        const hoursSinceLastConnection = (Date.now() - lastConnected.getTime()) / (1000 * 60 * 60);\n        \n        if (hoursSinceLastConnection > 24) {\n          issues.push('Device has been offline for more than 24 hours');\n          recommendations.push('Verify device is powered on and has internet access');\n        }\n      }\n      \n      return {\n        isHealthy: issues.length === 0,\n        issues,\n        recommendations\n      };\n    } catch (error) {\n      return {\n        isHealthy: false,\n        issues: ['Unable to perform health check'],\n        recommendations: ['Check device connectivity and try again']\n      };\n    }\n  }\n}\n\nexport const deviceService = new DeviceService();\nexport default deviceService;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAYlC,MAAMC,aAAa,CAAC;EAClB,MAAMC,UAAUA,CAAA,EAAwB;IACtC,OAAOF,UAAU,CAACG,GAAG,CAAa,UAAU,CAAC;EAC/C;EAEA,MAAMC,SAASA,CAACC,EAAU,EAAmB;IAC3C,OAAOL,UAAU,CAACG,GAAG,CAAS,YAAYE,EAAE,EAAE,CAAC;EACjD;EAEA,MAAMC,YAAYA,CAACC,UAA+B,EAAmB;IACnE,OAAOP,UAAU,CAACQ,IAAI,CAAS,UAAU,EAAED,UAAU,CAAC;EACxD;EAEA,MAAME,YAAYA,CAACJ,EAAU,EAAEE,UAA+B,EAAmB;IAC/E,OAAOP,UAAU,CAACU,GAAG,CAAS,YAAYL,EAAE,EAAE,EAAEE,UAAU,CAAC;EAC7D;EAEA,MAAMI,YAAYA,CAACN,EAAU,EAAiB;IAC5C,OAAOL,UAAU,CAACY,MAAM,CAAC,YAAYP,EAAE,EAAE,CAAC;EAC5C;EAEA,MAAMQ,aAAaA,CAACR,EAAU,EAAmC;IAC/D,OAAOL,UAAU,CAACQ,IAAI,CAAyB,YAAYH,EAAE,UAAU,CAAC;EAC1E;EAEA,MAAMS,gBAAgBA,CAACT,EAAU,EAAiB;IAChD,OAAOL,UAAU,CAACQ,IAAI,CAAC,YAAYH,EAAE,aAAa,CAAC;EACrD;EAEA,MAAMU,SAASA,CAACV,EAAU,EAA+B;IACvD,OAAOL,UAAU,CAACG,GAAG,CAAqB,YAAYE,EAAE,KAAK,CAAC;EAChE;EAEA,MAAMW,eAAeA,CAACX,EAAU,EAA6B;IAC3D,OAAOL,UAAU,CAACG,GAAG,CAAmB,YAAYE,EAAE,SAAS,CAAC;EAClE;EAEA,MAAMY,mBAAmBA,CAAA,EAAsB;IAC7C,OAAOjB,UAAU,CAACG,GAAG,CAAW,oBAAoB,CAAC;EACvD;EAEA,MAAMe,kBAAkBA,CAACb,EAAU,EAA4B;IAC7D,OAAOL,UAAU,CAACG,GAAG,CAAkB,YAAYE,EAAE,YAAY,CAAC;EACpE;EAEA,MAAMc,aAAaA,CAAA,EAA0B;IAC3C,OAAOnB,UAAU,CAACG,GAAG,CAAe,eAAe,CAAC;EACtD;;EAEA;EACA,MAAMiB,kBAAkBA,CAACC,QAAgB,EAAEC,QAAwC,EAAEC,QAAgB,GAAG,IAAI,EAAuB;IACjI,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACT,eAAe,CAACK,QAAQ,CAAC;QACnDC,QAAQ,CAACG,MAAM,CAAC;MAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;;IAED;IACA,MAAMF,UAAU,CAAC,CAAC;;IAElB;IACA,MAAMI,UAAU,GAAGC,WAAW,CAACL,UAAU,EAAED,QAAQ,CAAC;;IAEpD;IACA,OAAO,MAAMO,aAAa,CAACF,UAAU,CAAC;EACxC;;EAEA;EACA,MAAMG,sBAAsBA,CAACC,SAAmB,EAAqC;IACnF,MAAMC,QAAQ,GAAGD,SAAS,CAACE,GAAG,CAAC7B,EAAE,IAAI,IAAI,CAACQ,aAAa,CAACR,EAAE,CAAC,CAAC;IAC5D,OAAO8B,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;EAC9B;EAEA,MAAMI,yBAAyBA,CAACL,SAAmB,EAAiB;IAClE,MAAMC,QAAQ,GAAGD,SAAS,CAACE,GAAG,CAAC7B,EAAE,IAAI,IAAI,CAACS,gBAAgB,CAACT,EAAE,CAAC,CAAC;IAC/D,MAAM8B,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;EAC7B;EAEA,MAAMK,qBAAqBA,CAACN,SAAmB,EAAiB;IAC9D,MAAMC,QAAQ,GAAGD,SAAS,CAACE,GAAG,CAAC7B,EAAE,IAAI,IAAI,CAACM,YAAY,CAACN,EAAE,CAAC,CAAC;IAC3D,MAAM8B,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;EAC7B;;EAEA;EACA,MAAMM,kBAAkBA,CAAClB,QAAgB,EAItC;IACD,IAAI;MACF,MAAMmB,MAAM,GAAG,MAAM,IAAI,CAACpC,SAAS,CAACiB,QAAQ,CAAC;MAC7C,MAAMI,MAAM,GAAG,MAAM,IAAI,CAACT,eAAe,CAACK,QAAQ,CAAC;MAEnD,MAAMoB,MAAgB,GAAG,EAAE;MAC3B,MAAMC,eAAyB,GAAG,EAAE;;MAEpC;MACA,IAAI,CAACjB,MAAM,CAACkB,WAAW,EAAE;QACvBF,MAAM,CAACG,IAAI,CAAC,yBAAyB,CAAC;QACtCF,eAAe,CAACE,IAAI,CAAC,6BAA6B,CAAC;MACrD;;MAEA;MACA,IAAInB,MAAM,CAACoB,oBAAoB,GAAG,CAAC,EAAE;QACnCJ,MAAM,CAACG,IAAI,CAAC,yCAAyC,CAAC;QACtDF,eAAe,CAACE,IAAI,CAAC,iCAAiC,CAAC;MACzD;;MAEA;MACA,IAAIJ,MAAM,CAACM,aAAa,EAAE;QACxB,MAAMA,aAAa,GAAG,IAAIC,IAAI,CAACP,MAAM,CAACM,aAAa,CAAC;QACpD,MAAME,wBAAwB,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,GAAGH,aAAa,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;QAE1F,IAAIF,wBAAwB,GAAG,EAAE,EAAE;UACjCP,MAAM,CAACG,IAAI,CAAC,gDAAgD,CAAC;UAC7DF,eAAe,CAACE,IAAI,CAAC,qDAAqD,CAAC;QAC7E;MACF;MAEA,OAAO;QACLO,SAAS,EAAEV,MAAM,CAACW,MAAM,KAAK,CAAC;QAC9BX,MAAM;QACNC;MACF,CAAC;IACH,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACd,OAAO;QACLyB,SAAS,EAAE,KAAK;QAChBV,MAAM,EAAE,CAAC,gCAAgC,CAAC;QAC1CC,eAAe,EAAE,CAAC,yCAAyC;MAC7D,CAAC;IACH;EACF;AACF;AAEA,OAAO,MAAMW,aAAa,GAAG,IAAIpD,aAAa,CAAC,CAAC;AAChD,eAAeoD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}